"""Initial database schema

Revision ID: d53a55429d90
Revises: 
Create Date: 2025-06-02 17:12:06.283975

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd53a55429d90'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('system_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('stat_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('total_users', sa.Integer(), nullable=True),
    sa.Column('active_users', sa.Integer(), nullable=True),
    sa.Column('new_users', sa.Integer(), nullable=True),
    sa.Column('total_datasets', sa.Integer(), nullable=True),
    sa.Column('active_datasets', sa.Integer(), nullable=True),
    sa.Column('new_datasets', sa.Integer(), nullable=True),
    sa.Column('total_images', sa.Integer(), nullable=True),
    sa.Column('generated_images', sa.Integer(), nullable=True),
    sa.Column('annotated_images', sa.Integer(), nullable=True),
    sa.Column('total_models', sa.Integer(), nullable=True),
    sa.Column('active_models', sa.Integer(), nullable=True),
    sa.Column('training_jobs', sa.Integer(), nullable=True),
    sa.Column('total_storage_used', sa.Integer(), nullable=True),
    sa.Column('image_storage_used', sa.Integer(), nullable=True),
    sa.Column('model_storage_used', sa.Integer(), nullable=True),
    sa.Column('avg_generation_time', sa.Float(), nullable=True),
    sa.Column('avg_annotation_time', sa.Float(), nullable=True),
    sa.Column('system_uptime', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_stats_id'), 'system_stats', ['id'], unique=False)
    op.create_index(op.f('ix_system_stats_stat_date'), 'system_stats', ['stat_date'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_superuser', sa.Boolean(), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('ai_models',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('model_type', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('version', sa.String(length=50), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('checksum', sa.String(length=64), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('supported_targets', sa.JSON(), nullable=True),
    sa.Column('supported_weather', sa.JSON(), nullable=True),
    sa.Column('supported_terrain', sa.JSON(), nullable=True),
    sa.Column('accuracy', sa.Float(), nullable=True),
    sa.Column('inference_time', sa.Float(), nullable=True),
    sa.Column('memory_usage', sa.Integer(), nullable=True),
    sa.Column('training_dataset_size', sa.Integer(), nullable=True),
    sa.Column('training_epochs', sa.Integer(), nullable=True),
    sa.Column('training_loss', sa.Float(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('success_rate', sa.Float(), nullable=True),
    sa.Column('creator_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['creator_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_models_id'), 'ai_models', ['id'], unique=False)
    op.create_index(op.f('ix_ai_models_name'), 'ai_models', ['name'], unique=True)
    op.create_table('file_storages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('file_hash', sa.String(length=64), nullable=False),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('mime_type', sa.String(length=100), nullable=True),
    sa.Column('storage_type', sa.String(length=50), nullable=True),
    sa.Column('storage_config', sa.JSON(), nullable=True),
    sa.Column('reference_count', sa.Integer(), nullable=True),
    sa.Column('access_count', sa.Integer(), nullable=True),
    sa.Column('uploaded_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_accessed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['uploaded_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_file_storages_file_hash'), 'file_storages', ['file_hash'], unique=True)
    op.create_index(op.f('ix_file_storages_id'), 'file_storages', ['id'], unique=False)
    op.create_table('system_configs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('config_key', sa.String(length=100), nullable=False),
    sa.Column('config_type', sa.String(length=50), nullable=False),
    sa.Column('config_value', sa.JSON(), nullable=True),
    sa.Column('default_value', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_readonly', sa.Boolean(), nullable=True),
    sa.Column('validation_rules', sa.JSON(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_configs_config_key'), 'system_configs', ['config_key'], unique=True)
    op.create_index(op.f('ix_system_configs_id'), 'system_configs', ['id'], unique=False)
    op.create_table('system_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('level', sa.String(length=20), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('module', sa.String(length=100), nullable=True),
    sa.Column('function', sa.String(length=100), nullable=True),
    sa.Column('request_id', sa.String(length=100), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_logs_id'), 'system_logs', ['id'], unique=False)
    op.create_index(op.f('ix_system_logs_level'), 'system_logs', ['level'], unique=False)
    op.create_table('task_queues',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.String(length=255), nullable=False),
    sa.Column('task_type', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('task_data', sa.JSON(), nullable=True),
    sa.Column('result_data', sa.JSON(), nullable=True),
    sa.Column('worker_id', sa.String(length=100), nullable=True),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('error_traceback', sa.Text(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_queues_id'), 'task_queues', ['id'], unique=False)
    op.create_index(op.f('ix_task_queues_task_id'), 'task_queues', ['task_id'], unique=True)
    op.create_table('user_activities',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('action', sa.String(length=100), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_activities_id'), 'user_activities', ['id'], unique=False)
    op.create_table('user_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('last_accessed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_user_sessions_session_token'), 'user_sessions', ['session_token'], unique=True)
    op.create_table('model_evaluations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('model_id', sa.Integer(), nullable=False),
    sa.Column('evaluation_name', sa.String(length=255), nullable=False),
    sa.Column('test_dataset_id', sa.Integer(), nullable=True),
    sa.Column('accuracy', sa.Float(), nullable=True),
    sa.Column('precision', sa.Float(), nullable=True),
    sa.Column('recall', sa.Float(), nullable=True),
    sa.Column('f1_score', sa.Float(), nullable=True),
    sa.Column('fid_score', sa.Float(), nullable=True),
    sa.Column('is_score', sa.Float(), nullable=True),
    sa.Column('lpips_score', sa.Float(), nullable=True),
    sa.Column('evaluation_results', sa.JSON(), nullable=True),
    sa.Column('sample_images', sa.JSON(), nullable=True),
    sa.Column('evaluator_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['evaluator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['ai_models.id'], ),
    sa.ForeignKeyConstraint(['test_dataset_id'], ['datasets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_model_evaluations_id'), 'model_evaluations', ['id'], unique=False)
    op.create_table('model_training_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('model_id', sa.Integer(), nullable=False),
    sa.Column('job_name', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('progress', sa.Float(), nullable=True),
    sa.Column('training_config', sa.JSON(), nullable=True),
    sa.Column('dataset_config', sa.JSON(), nullable=True),
    sa.Column('final_loss', sa.Float(), nullable=True),
    sa.Column('final_accuracy', sa.Float(), nullable=True),
    sa.Column('training_logs', sa.Text(), nullable=True),
    sa.Column('gpu_hours', sa.Float(), nullable=True),
    sa.Column('memory_peak', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('creator_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['creator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['ai_models.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_model_training_jobs_id'), 'model_training_jobs', ['id'], unique=False)
    op.add_column('annotations', sa.Column('is_auto_generated', sa.Boolean(), nullable=True))
    op.add_column('annotations', sa.Column('is_verified', sa.Boolean(), nullable=True))
    op.add_column('annotations', sa.Column('annotator_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'annotations', 'users', ['annotator_id'], ['id'])
    op.add_column('datasets', sa.Column('creator_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'datasets', 'users', ['creator_id'], ['id'])
    op.add_column('generation_tasks', sa.Column('generation_config', sa.JSON(), nullable=True))
    op.add_column('generation_tasks', sa.Column('model_id', sa.Integer(), nullable=True))
    op.add_column('generation_tasks', sa.Column('creator_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'generation_tasks', 'users', ['creator_id'], ['id'])
    op.create_foreign_key(None, 'generation_tasks', 'ai_models', ['model_id'], ['id'])
    op.add_column('images', sa.Column('quality_score', sa.Float(), nullable=True))
    op.add_column('images', sa.Column('is_validated', sa.Boolean(), nullable=True))
    op.add_column('images', sa.Column('file_storage_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'images', 'file_storages', ['file_storage_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'images', type_='foreignkey')
    op.drop_column('images', 'file_storage_id')
    op.drop_column('images', 'is_validated')
    op.drop_column('images', 'quality_score')
    op.drop_constraint(None, 'generation_tasks', type_='foreignkey')
    op.drop_constraint(None, 'generation_tasks', type_='foreignkey')
    op.drop_column('generation_tasks', 'creator_id')
    op.drop_column('generation_tasks', 'model_id')
    op.drop_column('generation_tasks', 'generation_config')
    op.drop_constraint(None, 'datasets', type_='foreignkey')
    op.drop_column('datasets', 'creator_id')
    op.drop_constraint(None, 'annotations', type_='foreignkey')
    op.drop_column('annotations', 'annotator_id')
    op.drop_column('annotations', 'is_verified')
    op.drop_column('annotations', 'is_auto_generated')
    op.drop_index(op.f('ix_model_training_jobs_id'), table_name='model_training_jobs')
    op.drop_table('model_training_jobs')
    op.drop_index(op.f('ix_model_evaluations_id'), table_name='model_evaluations')
    op.drop_table('model_evaluations')
    op.drop_index(op.f('ix_user_sessions_session_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index(op.f('ix_user_activities_id'), table_name='user_activities')
    op.drop_table('user_activities')
    op.drop_index(op.f('ix_task_queues_task_id'), table_name='task_queues')
    op.drop_index(op.f('ix_task_queues_id'), table_name='task_queues')
    op.drop_table('task_queues')
    op.drop_index(op.f('ix_system_logs_level'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_id'), table_name='system_logs')
    op.drop_table('system_logs')
    op.drop_index(op.f('ix_system_configs_id'), table_name='system_configs')
    op.drop_index(op.f('ix_system_configs_config_key'), table_name='system_configs')
    op.drop_table('system_configs')
    op.drop_index(op.f('ix_file_storages_id'), table_name='file_storages')
    op.drop_index(op.f('ix_file_storages_file_hash'), table_name='file_storages')
    op.drop_table('file_storages')
    op.drop_index(op.f('ix_ai_models_name'), table_name='ai_models')
    op.drop_index(op.f('ix_ai_models_id'), table_name='ai_models')
    op.drop_table('ai_models')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_system_stats_stat_date'), table_name='system_stats')
    op.drop_index(op.f('ix_system_stats_id'), table_name='system_stats')
    op.drop_table('system_stats')
    # ### end Alembic commands ###
