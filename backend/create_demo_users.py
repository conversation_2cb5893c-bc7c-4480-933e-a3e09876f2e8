#!/usr/bin/env python3
"""
创建演示用户数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine, Base
from app.core.security import get_password_hash
from app.models.user import User

def create_demo_users():
    """创建演示用户"""
    
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    # 获取数据库会话
    db: Session = SessionLocal()
    
    try:
        # 检查是否已存在演示用户
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("演示用户已存在，跳过创建")
            return
        
        # 创建管理员用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="系统管理员",
            hashed_password=get_password_hash("admin123"),
            role="admin",
            is_active=True
        )
        db.add(admin_user)
        
        # 创建研究员用户
        researcher_user = User(
            username="researcher",
            email="<EMAIL>", 
            full_name="军事研究员",
            hashed_password=get_password_hash("research123"),
            role="researcher",
            is_active=True
        )
        db.add(researcher_user)
        
        # 创建普通用户
        user = User(
            username="user",
            email="<EMAIL>",
            full_name="普通用户",
            hashed_password=get_password_hash("user123"),
            role="user",
            is_active=True
        )
        db.add(user)
        
        # 提交更改
        db.commit()
        
        print("演示用户创建成功:")
        print("- 管理员: admin / admin123")
        print("- 研究员: researcher / research123") 
        print("- 普通用户: user / user123")
        
    except Exception as e:
        print(f"创建演示用户失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_demo_users() 