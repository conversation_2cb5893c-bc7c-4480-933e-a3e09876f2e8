# 构建工具 - 兼容版本
setuptools<70.0.0
wheel>=0.37.0

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
redis==5.0.1

# AI和图像处理 - 兼容版本
torch>=2.2.0
torchvision>=0.17.0
diffusers==0.24.0
transformers==4.35.2
accelerate==0.24.1
opencv-python==********
Pillow>=10.0.0,<11.0.0
numpy>=1.24.0,<2.0.0
matplotlib==3.7.2

# 数据处理
pandas>=2.0.0,<3.0.0
scikit-learn>=1.3.0,<2.0.0

# 工具库
pydantic>=2.5.0,<3.0.0
pydantic-settings==2.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
httpx>=0.25.0,<1.0.0
aiofiles>=23.0.0,<24.0.0

# 日志和监控
loguru==0.7.2
prometheus-client>=0.19.0,<1.0.0

# 开发工具
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
black>=23.0.0,<24.0.0
isort>=5.12.0,<6.0.0
flake8>=6.0.0,<7.0.0

# 其他
requests>=2.31.0,<3.0.0
celery>=5.3.0,<6.0.0
flower>=2.0.0,<3.0.0 