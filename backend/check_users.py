#!/usr/bin/env python3
"""
检查和创建演示用户
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine, Base
from app.core.security import get_password_hash
from app.models.user import User

def check_and_create_users():
    """检查和创建演示用户"""
    
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    # 获取数据库会话
    db: Session = SessionLocal()
    
    try:
        # 检查现有用户
        users = db.query(User).all()
        print(f'数据库中现有用户数量: {len(users)}')
        
        for user in users:
            print(f'用户: {user.username}, 邮箱: {user.email}, 角色: {user.role}')
        
        # 如果没有用户，创建演示用户
        if len(users) == 0:
            print('创建演示用户...')
            
            # 创建管理员用户
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="系统管理员",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                is_active=True
            )
            db.add(admin_user)
            
            # 创建研究员用户
            researcher_user = User(
                username="researcher",
                email="<EMAIL>", 
                full_name="军事研究员",
                hashed_password=get_password_hash("research123"),
                role="researcher",
                is_active=True
            )
            db.add(researcher_user)
            
            # 提交更改
            db.commit()
            print('演示用户创建成功!')
            print('- 管理员: admin / admin123')
            print('- 研究员: researcher / research123')
        else:
            print('用户已存在，无需创建')
        
    except Exception as e:
        print(f"操作失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    check_and_create_users() 