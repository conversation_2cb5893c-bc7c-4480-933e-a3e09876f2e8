"""
用户认证端点
"""

from datetime import timedelta
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session

from app.api import deps
from app.core import security
from app.core.config import settings
from app.core.security import get_password_hash, verify_password
from app.crud import crud_user
from app.models.user import User
from app.schemas.auth import Token, UserCreate, UserResponse, UserUpdate

router = APIRouter()


@router.post("/login")
def login_for_access_token(
    *,
    db: Session = Depends(deps.get_db),
    username: str = Body(...),
    password: str = Body(...)
) -> Any:
    """
    用户登录
    """
    # 先尝试用邮箱登录
    user = crud_user.authenticate(db, email=username, password=password)
    if not user:
        # 再尝试用用户名登录
        user = crud_user.get_by_username(db, username=username)
        if not user or not verify_password(password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        user.id, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role,
            "is_active": user.is_active,
            "created_at": user.created_at,
            "last_login_at": user.last_login_at
        }
    }


@router.post("/register", response_model=UserResponse)
def register(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
) -> Any:
    """
    创建新用户
    """
    user = crud_user.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="该邮箱已被注册",
        )
    
    user = crud_user.get_by_username(db, username=user_in.username)
    if user:
        raise HTTPException(
            status_code=400,
            detail="该用户名已被使用",
        )
    
    user = crud_user.create(db, obj_in=user_in)
    return user


@router.get("/me", response_model=UserResponse)
def read_users_me(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    获取当前用户信息
    """
    return current_user


@router.put("/profile", response_model=UserResponse)
def update_user_me(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    更新当前用户信息
    """
    user = crud_user.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.post("/change-password")
def change_password(
    *,
    db: Session = Depends(deps.get_db),
    current_password: str = Body(...),
    new_password: str = Body(...),
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    修改密码
    """
    if not verify_password(current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="当前密码错误"
        )
    
    hashed_password = get_password_hash(new_password)
    crud_user.update(
        db, 
        db_obj=current_user, 
        obj_in={"hashed_password": hashed_password}
    )
    
    return {"message": "密码修改成功"}


@router.post("/logout")
def logout(
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    用户登出
    """
    # 在实际应用中，这里可以将token加入黑名单
    return {"message": "登出成功"} 