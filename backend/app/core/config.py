"""
应用配置文件
"""

import os
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    PROJECT_NAME: str = "军事目标图像数据集生成系统"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 3002
    DEBUG: bool = True
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8天
    
    # 数据库配置 - 使用SQLite进行开发测试
    DATABASE_URL: str = "sqlite:///./picgen.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 文件存储配置
    UPLOAD_DIR: str = "data/uploads"
    DATASET_DIR: str = "data/datasets"
    MODEL_DIR: str = "models"
    STATIC_DIR: str = "static"
    
    # AI模型配置
    STABLE_DIFFUSION_MODEL: str = "runwayml/stable-diffusion-v1-5"
    CUSTOM_MODEL_PATH: str = "models/custom"
    DEVICE: str = "cuda"  # cuda 或 cpu
    
    # 图像生成配置
    DEFAULT_IMAGE_SIZE: tuple = (512, 512)
    MAX_BATCH_SIZE: int = 10
    DEFAULT_INFERENCE_STEPS: int = 50
    DEFAULT_GUIDANCE_SCALE: float = 7.5
    
    # 数据集配置
    TRAIN_SPLIT: float = 0.8
    VAL_SPLIT: float = 0.1
    TEST_SPLIT: float = 0.1
    
    # 军事目标类型
    MILITARY_TARGETS: List[str] = [
        "tank",      # 坦克
        "aircraft",  # 战机
        "ship",      # 舰艇
        "vehicle",   # 军用车辆
        "helicopter", # 直升机
        "submarine"  # 潜艇
    ]
    
    # 天气条件
    WEATHER_CONDITIONS: List[str] = [
        "sunny",     # 晴天
        "rainy",     # 雨天
        "snowy",     # 雪天
        "foggy",     # 大雾
        "night",     # 夜间
        "cloudy"     # 多云
    ]
    
    # 地形场景
    TERRAIN_SCENES: List[str] = [
        "urban",     # 城市
        "island",    # 岛屿
        "rural",     # 乡村
        "desert",    # 沙漠
        "forest",    # 森林
        "mountain"   # 山地
    ]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局设置实例
settings = Settings() 