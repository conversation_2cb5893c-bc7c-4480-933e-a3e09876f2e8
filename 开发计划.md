# 军事目标图像数据集生成系统 - 项目总结

## 项目概述

本项目是一个基于AI的军事目标图像数据集生成和管理平台，支持在不同天气和地形场景中生成自然、清晰、合理的模拟图像数据集。

## 已完成功能

### ✅ 第一阶段：基础架构 (已完成)

#### 1. 项目结构搭建
- [x] 前后端分离架构
- [x] Docker容器化部署
- [x] 开发环境配置
- [x] 代码结构规范

#### 2. 数据库设计 (已完成)
- [x] **完整的数据模型设计**：16个核心数据表
- [x] **用户管理模块**：用户、会话、活动日志
- [x] **数据集管理模块**：数据集、图像、标注、生成任务
- [x] **AI模型管理模块**：模型、训练任务、评估结果
- [x] **系统管理模块**：配置、统计、日志、任务队列、文件存储
- [x] **数据库迁移配置**：Alembic迁移管理
- [x] **关系设计**：完整的外键约束和索引优化

#### 3. 后端API框架
- [x] FastAPI框架搭建
- [x] SQLAlchemy ORM配置
- [x] PostgreSQL + Redis集成
- [x] 基础配置管理
- [x] 端口配置优化 (3002)

#### 4. 前端框架搭建
- [x] React 18 + TypeScript
- [x] 现代化UI设计
- [x] 响应式布局
- [x] 端口配置优化 (3001)

## 技术架构

### 数据库架构
```
PostgreSQL (主数据库)
├── 用户管理模块 (3张表)
│   ├── users - 用户基本信息
│   ├── user_sessions - 会话管理
│   └── user_activities - 操作日志
├── 数据集管理模块 (4张表)
│   ├── datasets - 数据集信息
│   ├── images - 图像文件
│   ├── annotations - 标注数据
│   └── generation_tasks - 生成任务
├── AI模型管理模块 (3张表)
│   ├── ai_models - 模型信息
│   ├── model_training_jobs - 训练任务
│   └── model_evaluations - 评估结果
└── 系统管理模块 (5张表)
    ├── system_configs - 系统配置
    ├── system_stats - 统计数据
    ├── system_logs - 系统日志
    ├── task_queues - 任务队列
    └── file_storages - 文件存储

Redis (缓存数据库)
├── 用户会话缓存
├── 系统配置缓存
└── 任务队列缓存
```

### 服务架构
```
前端 (React) :3001
    ↓ HTTP API
后端 (FastAPI) :3002
    ↓ SQL/ORM
PostgreSQL :5432
    ↓ 缓存
Redis :6379
```

## 核心特性

### 数据库特性
- 🔗 **完整关系设计**：16个表，完整的外键约束
- 📊 **性能优化**：关键字段索引，查询优化
- 🕒 **时间戳追踪**：自动记录创建和更新时间
- 🔄 **数据完整性**：外键约束确保数据一致性
- 📈 **统计分析**：内置数据统计和分析功能
- 🔧 **配置管理**：灵活的系统配置机制
- 📝 **审计日志**：完整的用户操作和系统日志

### 系统特性
- 👥 **多用户支持**：角色权限管理 (admin/researcher/viewer)
- 🎯 **多场景生成**：支持不同军事目标、天气、地形
- 🏷️ **智能标注**：分类和检测标注支持
- 🤖 **AI模型管理**：模型训练、评估、版本控制
- 📊 **数据可视化**：统计分析和监控面板
- 🔄 **任务队列**：异步任务处理机制

## 部署状态

### 当前运行服务
- ✅ PostgreSQL: localhost:5432
- ✅ Redis: localhost:6379
- ✅ 后端API: http://localhost:3002
- ✅ 前端应用: http://localhost:3001

### 数据库状态
- ✅ 16个数据表已创建
- ✅ 索引和约束已建立
- ✅ 迁移系统已配置
- ✅ 数据完整性验证通过

## 开发进度

### ✅ 已完成功能
- [x] **项目架构设计** - 前后端分离架构完成
- [x] **数据库完整设计** - 16个核心数据表，完整关系设计
- [x] **前后端基础框架** - FastAPI + React + TypeScript
- [x] **容器化部署配置** - Docker + Docker Compose
- [x] **开发环境搭建** - 完整的开发环境配置
- [x] **数据库迁移系统** - Alembic迁移管理
- [x] **用户认证系统** - JWT认证，用户注册/登录/权限管理
- [x] **数据集管理服务** - 完整的CRUD操作和统计功能
- [x] **前端页面框架** - 仪表盘、数据集管理、图像生成等页面
- [x] **API服务架构** - RESTful API设计和实现
- [x] **前端服务层** - 完整的API客户端服务

### 🚧 进行中功能
- [x] **数据集管理API** - 基础CRUD已完成，需要完善图像和标注API
- [ ] **图像生成引擎** - 生成服务框架已搭建，需要集成AI模型
- [ ] **前端UI组件** - 基础组件已完成，需要完善交互功能
- [ ] **文件上传系统** - 需要实现图像上传和存储管理

### 📋 待开发功能
- [ ] **AI模型集成** - Stable Diffusion集成和自定义模型
- [ ] **自动标注系统** - COCO格式标注生成
- [ ] **批量处理系统** - 大规模图像生成和处理
- [ ] **监控系统** - 系统性能和任务监控
- [ ] **数据导出功能** - 标准格式数据集导出

## 技术栈

### 后端技术
- **框架**: FastAPI 0.104+
- **数据库**: PostgreSQL 13+ / Redis 6+
- **ORM**: SQLAlchemy 2.0+
- **迁移**: Alembic 1.12+
- **语言**: Python 3.10+

### 前端技术
- **框架**: React 18 + TypeScript
- **构建**: Create React App
- **样式**: CSS3 + 现代化设计
- **状态**: 本地状态管理

### 部署技术
- **容器**: Docker + Docker Compose
- **代理**: Nginx (待配置)
- **监控**: 待集成

## 文档

- 📖 [README.md](README.md) - 项目介绍和快速开始
- 🗄️ [DATABASE_DESIGN.md](DATABASE_DESIGN.md) - 详细数据库设计文档
- 📋 [PROJECT_SUMMARY.md](PROJECT_SUMMARY.md) - 项目总结 (本文档)

## 详细功能需求

### 核心业务需求
**作战环境下军事目标数据集生成与管理平台**

#### 支持的军事目标类型
- 🚗 **坦克** (Tank)
- ✈️ **战机** (Fighter Aircraft)
- 🚢 **舰艇** (Warship)

#### 支持的天气场景
- 🌧️ **雨天** (Rainy)
- ❄️ **雪天** (Snowy)
- 🌫️ **大雾** (Foggy)
- 🌙 **夜间** (Night)

#### 支持的地形场景
- 🏙️ **城市** (Urban)
- 🏝️ **岛屿** (Island)
- 🌾 **乡村** (Rural)

### 功能模块详细规划

## 第二阶段：核心功能开发 (当前阶段)

### 1. 用户界面系统 (GUI) ✅ 基本完成
#### 1.1 系统兼容性 ✅ 已完成
- [x] **运行环境**: Windows/Linux双平台支持
- [x] **前端框架**: React 18 + TypeScript + Ant Design
- [x] **响应式设计**: 适配不同屏幕尺寸

#### 1.2 用户认证与权限 ✅ 已完成
- [x] **JWT令牌认证** - 完整的认证流程
- [x] **角色权限控制** - admin/researcher/viewer角色
- [x] **用户注册登录** - 注册、登录、登出功能
- [x] **会话管理** - 自动会话管理和保护路由

#### 1.3 主界面功能 ✅ 已完成
- [x] **数据集管理面板** - 完整的数据集列表和管理界面
- [x] **图像生成控制台** - 生成任务管理界面
- [x] **标注预览界面** - 图像标注预览组件
- [x] **统计分析面板** - 系统仪表盘和统计图表

### 2. 图像数据集生成系统

#### 2.1 生成参数控制
- [ ] **生成数量设置**: 用户可设定1-10,000张图片
- [ ] **目标选择**:
  - 单一军事目标生成
  - 混合军事目标随机生成
- [ ] **场景选择**:
  - 单一天气/地形场景
  - 混合随机场景生成
- [ ] **分辨率控制**: 标准分辨率(非2K/4K)

#### 2.2 图像合成方式
- [ ] **传统代码图像合成**
  - 基于模板的图像合成
  - 场景背景替换
  - 目标物体叠加
- [ ] **AI生成方式**
  - Stable Diffusion 1.5/2.1集成
  - 自定义Prompt生成
  - 批量生成管道

#### 2.3 生成组合矩阵
```
天气 × 地形 × 目标 = 生成组合
4种天气 × 3种地形 × 3种目标 = 36种基础组合
支持随机组合和指定组合生成
```

### 3. 智能标注系统

#### 3.1 自动标注功能
- [ ] **分类标注**: 自动识别军事目标类型
- [ ] **检测标注**: 自动生成Bounding Box
- [ ] **COCO格式输出**: 标准JSON格式
- [ ] **标注质量控制**: 模拟近似标注

#### 3.2 标注预览功能
- [ ] **实时预览**: 临时bbox框显示
- [ ] **标注编辑**: 手动调整标注框
- [ ] **标注验证**: 标注质量检查
- [ ] **批量标注**: 批量处理标注数据

#### 3.3 标注数据格式
```json
{
  "images": [...],
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "bbox": [x, y, width, height],
      "area": 1000,
      "iscrowd": 0
    }
  ],
  "categories": [
    {"id": 1, "name": "tank"},
    {"id": 2, "name": "aircraft"},
    {"id": 3, "name": "warship"}
  ]
}
```

### 4. 数据集管理系统 ✅ 基本完成

#### 4.1 数据集CRUD操作 ✅ 已完成
- [x] **Create**: 创建新数据集
  - [x] 数据集命名和验证
  - [x] 描述信息设置
  - [x] 生成参数配置
  - [x] 自动目录结构创建
- [x] **Read**: 数据集查询显示
  - [x] 数据集列表展示（分页、搜索、筛选）
  - [x] 详细信息查看
  - [x] 完整统计信息显示
- [x] **Update**: 数据集更新
  - [x] 重命名数据集
  - [x] 更新描述信息
  - [x] 修改配置参数
  - [x] 状态管理
- [x] **Delete**: 数据集删除
  - [x] 安全删除确认
  - [x] 级联删除相关数据
  - [x] 存储空间释放

#### 4.2 数据集统计功能 ✅ 已完成
- [x] **基础统计**:
  - [x] 图片总数量和生成进度
  - [x] 各类目标数量分布
  - [x] 天气和地形场景统计
  - [x] 数据分割统计
- [x] **高级分析**:
  - [x] 完成率计算
  - [x] 标注数量统计
  - [x] 数据集平衡性分析

#### 4.3 自动数据分组 ✅ 已完成
- [x] **训练集**: 80% 自动分配
- [x] **验证集**: 10% 自动分配
- [x] **测试集**: 10% 自动分配
- [x] **分组策略**: 保证各类别均衡分布
- [x] **目录结构**: 自动创建标准目录结构

### 5. 存储系统设计

#### 5.1 文件存储结构
```
datasets/
├── dataset_001/
│   ├── images/
│   │   ├── train/
│   │   ├── val/
│   │   └── test/
│   ├── annotations/
│   │   ├── train.json
│   │   ├── val.json
│   │   └── test.json
│   └── metadata.json
└── dataset_002/
    └── ...
```

#### 5.2 数据库存储
- [x] **PostgreSQL**: 元数据和关系数据
- [x] **Redis**: 缓存和会话数据
- [ ] **文件系统**: 图像文件存储
- [ ] **备份机制**: 数据备份和恢复

## 第三阶段：AI模型集成

### 1. Stable Diffusion集成

#### 1.1 模型部署
- [ ] **本地部署**: Stable Diffusion 1.5/2.1
- [ ] **GPU支持**: CUDA加速推理
- [ ] **模型管理**: 版本控制和切换
- [ ] **资源监控**: GPU/内存使用监控

#### 1.2 生成控制
- [ ] **Prompt工程**: 智能提示词生成
- [ ] **Pipeline控制**: 生成参数调优
- [ ] **批量生成**: 高效批处理
- [ ] **质量控制**: 生成结果筛选

#### 1.3 错误处理
- [ ] **重试机制**: 生成失败自动重试
- [ ] **异常处理**: 模型推理异常捕获
- [ ] **日志记录**: 详细的生成日志
- [ ] **性能优化**: 推理速度优化

### 2. 自定义模型训练

#### 2.1 模型微调
- [ ] **Stable Diffusion微调**: 基于军事目标数据
- [ ] **LoRA训练**: 轻量级适应性训练
- [ ] **数据预处理**: 训练数据清洗和增强
- [ ] **训练监控**: 实时训练进度监控

#### 2.2 训练数据要求
- **数据量**: 每类100-200张以上
- **数据质量**: 高质量标注数据
- **数据多样性**: 多场景多角度覆盖
- **数据格式**: 标准化输入格式

#### 2.3 模型评估
- [ ] **生成质量评估**: FID、IS等指标
- [ ] **目标识别准确率**: 分类准确性
- [ ] **场景真实性**: 视觉合理性评估
- [ ] **A/B测试**: 模型效果对比

### 3. 性能优化

#### 3.1 推理优化
- [ ] **模型量化**: 减少模型大小
- [ ] **批处理优化**: 提高吞吐量
- [ ] **内存管理**: 优化内存使用
- [ ] **并行处理**: 多GPU并行推理

#### 3.2 系统优化
- [ ] **缓存策略**: 智能缓存机制
- [ ] **异步处理**: 非阻塞任务处理
- [ ] **负载均衡**: 请求分发优化
- [ ] **监控告警**: 系统健康监控

## 第四阶段：系统完善

### 1. 监控与日志
- [ ] **系统监控**: 服务状态监控
- [ ] **性能监控**: 响应时间和吞吐量
- [ ] **错误监控**: 异常和错误追踪
- [ ] **用户行为分析**: 使用情况统计

### 2. 安全与权限
- [ ] **数据安全**: 敏感数据加密
- [ ] **访问控制**: 细粒度权限管理
- [ ] **审计日志**: 操作记录追踪
- [ ] **备份恢复**: 数据备份策略

### 3. 部署与运维
- [ ] **容器化部署**: Docker生产环境
- [ ] **CI/CD流水线**: 自动化部署
- [ ] **负载均衡**: 高可用架构
- [ ] **监控告警**: 运维监控体系

## 交付标准与验收条件

### 用户界面交付标准
- ✅ **跨平台支持**: Windows/Linux环境运行
- ✅ **现代化界面**: React 18 + TypeScript实现
- [ ] **用户友好**: 直观的操作界面和流程
- [ ] **响应式设计**: 适配不同分辨率屏幕
- [ ] **操作反馈**: 实时进度显示和状态提示

### 图像生成交付标准
- [ ] **生成能力**: 单次最多10,000张图片
- [ ] **质量标准**: 视觉合理，清晰自然
- [ ] **组合完整**: 支持36种基础场景组合
- [ ] **格式标准**: 标准分辨率，非超高清
- [ ] **生成速度**: 合理的生成时间(依赖GPU性能)

### 标注系统交付标准
- [ ] **自动标注**: 生成时自动创建标注信息
- [ ] **格式标准**: COCO JSON格式输出
- [ ] **预览功能**: 临时bbox框预览(不污染原图)
- [ ] **准确性**: 模拟近似标注，非人工精度
- [ ] **编辑功能**: 支持手动调整标注框

### 数据集管理交付标准
- [ ] **CRUD完整**: 增删改查功能完整
- [ ] **统计分析**: 详细的数据集统计信息
- [ ] **自动分组**: 训练/验证/测试集自动分配
- [ ] **存储结构**: 标准化的文件组织结构
- [ ] **数据完整性**: 确保数据一致性和完整性

### AI模型交付标准
- [ ] **基准模型**: Stable Diffusion 1.5/2.1本地部署
- [ ] **自定义模型**: 基于军事目标的微调模型
- [ ] **模型文档**: 详细的使用说明和API文档
- [ ] **推理代码**: 完整的推理代码和示例
- [ ] **性能要求**: 合理的推理速度和资源占用

## 当前开发状态总结

### 🎉 第一阶段：基础设施 (已完成 100%)
- ✅ **项目架构** - 前后端分离架构完成
- ✅ **数据库设计** - 16个核心表，完整关系设计
- ✅ **开发环境** - Docker容器化部署
- ✅ **基础框架** - FastAPI + React + TypeScript

### 🚀 第二阶段：核心功能 (已完成 80%)
- ✅ **用户认证系统** - JWT认证，角色权限管理
- ✅ **数据集管理** - 完整CRUD操作，统计分析
- ✅ **前端界面** - 仪表盘，数据集管理，生成控制台
- ✅ **API服务** - RESTful API设计和实现
- 🚧 **图像生成引擎** - 框架已搭建，需要AI模型集成
- 🚧 **文件上传系统** - 需要完善图像上传和存储

### 📋 下一步开发计划

#### 即将开始：图像生成系统集成 (Week 1-2)
1. **完善API路由注册**
   - 将数据集和生成相关API注册到主路由
   - 完善错误处理和响应格式

2. **图像上传和存储**
   - 实现多文件上传功能
   - 图像文件存储和管理
   - 缩略图生成和预览

3. **传统图像合成**
   - 基于模板的图像合成
   - 场景背景替换
   - 目标物体叠加

#### 第三阶段：AI模型集成 (Week 3-6)
1. **Stable Diffusion集成**
   - 模型下载和部署
   - GPU环境配置
   - 推理接口开发

2. **自动标注系统**
   - COCO格式标注生成
   - 边界框自动检测
   - 标注预览和编辑

3. **批量生成系统**
   - 任务队列管理
   - 进度监控
   - 错误处理和重试

#### 第四阶段：系统完善 (Week 7-8)
1. **性能优化**
2. **监控系统**
3. **部署优化**
4. **文档完善**

## 风险评估与应对策略

### 技术风险
1. **AI生成不稳定性**
   - 风险: 模型推理效果不稳定，可能产生重复或抽象结果
   - 应对: 实现重试机制，质量筛选，多模型备选

2. **GPU资源依赖**
   - 风险: 需要甲方提供足够的GPU资源
   - 应对: 提供详细的硬件需求说明，支持CPU降级运行

3. **训练数据质量**
   - 风险: 训练效果依赖甲方提供的数据质量
   - 应对: 提供数据质量评估工具，数据预处理建议

### 项目风险
1. **开发周期风险**
   - 风险: AI模型训练时间不可控
   - 应对: 分阶段交付，优先保证基础功能

2. **性能要求风险**
   - 风险: 生成速度可能不满足用户期望
   - 应对: 提前说明性能预期，提供性能优化选项

## 质量保证计划

### 代码质量
- [ ] **代码审查**: 所有代码提交前进行审查
- [ ] **单元测试**: 核心功能单元测试覆盖率>80%
- [ ] **集成测试**: 端到端功能测试
- [ ] **性能测试**: 负载测试和压力测试

### 用户体验
- [ ] **可用性测试**: 用户界面易用性测试
- [ ] **兼容性测试**: 多平台兼容性验证
- [ ] **错误处理**: 友好的错误提示和处理
- [ ] **文档完善**: 详细的用户手册和API文档

## 部署与运维计划

### 部署环境
```yaml
生产环境配置:
  操作系统: Windows Server 2019+ / Ubuntu 20.04+
  Python: 3.10+
  Node.js: 18+
  PostgreSQL: 13+
  Redis: 6+
  GPU: NVIDIA RTX 3080+ (推荐)
  内存: 32GB+ (推荐)
  存储: 1TB+ SSD
```

### 监控体系
- [ ] **应用监控**: 服务健康状态监控
- [ ] **性能监控**: 响应时间和吞吐量监控
- [ ] **资源监控**: CPU/GPU/内存使用监控
- [ ] **日志管理**: 集中化日志收集和分析

## 联系信息

- **项目维护者**: 开发团队
- **技术栈**: FastAPI + React + PostgreSQL + Stable Diffusion
- **部署方式**: Docker容器化 + GPU加速
- **开发状态**: 数据库设计已完成，进入核心功能开发阶段
- **预计交付**: 12周完整开发周期

---

## 附录

### A. 支持的场景组合矩阵
| 天气 | 城市 | 岛屿 | 乡村 |
|------|------|------|------|
| 雨天 | ✅ | ✅ | ✅ |
| 雪天 | ✅ | ✅ | ✅ |
| 大雾 | ✅ | ✅ | ✅ |
| 夜间 | ✅ | ✅ | ✅ |

### B. 军事目标类别定义
```json
{
  "categories": [
    {
      "id": 1,
      "name": "tank",
      "display_name": "坦克",
      "description": "各类主战坦克、装甲车辆"
    },
    {
      "id": 2,
      "name": "aircraft",
      "display_name": "战机",
      "description": "战斗机、攻击机、轰炸机等军用飞机"
    },
    {
      "id": 3,
      "name": "warship",
      "display_name": "舰艇",
      "description": "驱逐舰、护卫舰、航母等军用舰艇"
    }
  ]
}
```

### C. 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   AI推理引擎    │
│   React App     │◄──►│   FastAPI       │◄──►│ Stable Diffusion│
│   :3001         │    │   :3002         │    │   GPU加速       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   数据存储      │              │
         │              │ PostgreSQL+Redis│              │
         │              │   :5432/:6379   │              │
         │              └─────────────────┘              │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                        ┌─────────────────┐
                        │   文件存储      │
                        │  图像+标注数据  │
                        │   本地文件系统  │
                        └─────────────────┘
```


graph LR
    subgraph "数据输入"
        INPUT1[军事目标样本<br/>坦克/战机/舰艇]
        INPUT2[场景参数<br/>天气/地形]
        INPUT3[生成参数<br/>数量/分辨率]
    end
    
    subgraph "图像生成引擎"
        ENGINE[生成引擎]
        TRAD[传统合成方式]
        AI[AI生成方式]
        
        ENGINE --> TRAD
        ENGINE --> AI
    end
    
    subgraph "AI模型"
        SD15[Stable Diffusion 1.5]
        SD21[Stable Diffusion 2.1] 
        CUSTOM[自定义微调模型]
        
        AI --> SD15
        AI --> SD21
        AI --> CUSTOM
    end
    
    subgraph "标注系统"
        AUTO[自动标注]
        CLASSIFY[分类标注]
        DETECT[检测标注]
        PREVIEW[预览功能]
        
        AUTO --> CLASSIFY
        AUTO --> DETECT
        AUTO --> PREVIEW
    end
    
    subgraph "数据集管理"
        CREATE[创建数据集]
        UPDATE[更新数据集]
        DELETE[删除数据集]
        QUERY[查询统计]
        SPLIT[自动分组]
        
        CREATE --> SPLIT
        QUERY --> SPLIT
    end
    
    subgraph "输出格式"
        IMAGES[图像文件]
        COCO[COCO JSON标注]
        TRAIN[训练集 80%]
        VAL[验证集 10%]
        TEST[测试集 10%]
    end
    
    INPUT1 --> ENGINE
    INPUT2 --> ENGINE
    INPUT3 --> ENGINE
    
    ENGINE --> AUTO
    AUTO --> CREATE
    
    CREATE --> IMAGES
    CREATE --> COCO
    CREATE --> TRAIN
    CREATE --> VAL
    CREATE --> TEST
    
    style INPUT1 fill:#e3f2fd
    style ENGINE fill:#f3e5f5
    style AUTO fill:#fff3e0
    style CREATE fill:#e8f5e8
    style IMAGES fill:#fce4ec


graph TB
    subgraph "用户界面层"
        UI[React前端界面<br/>:3001]
        UI --> |HTTP API| API
    end
    
    subgraph "应用服务层"
        API[FastAPI后端<br/>:3002]
        AUTH[用户认证模块]
        DATASET[数据集管理模块]
        GEN[图像生成模块]
        ANNO[标注系统模块]
        
        API --> AUTH
        API --> DATASET
        API --> GEN
        API --> ANNO
    end
    
    subgraph "AI推理层"
        SD[Stable Diffusion<br/>基准模型]
        CUSTOM[自定义微调模型]
        TRAD[传统图像合成]
        
        GEN --> SD
        GEN --> CUSTOM
        GEN --> TRAD
    end
    
    subgraph "数据存储层"
        PG[(PostgreSQL<br/>:5432)]
        REDIS[(Redis<br/>:6379)]
        FILES[文件系统<br/>图像存储]
        
        DATASET --> PG
        AUTH --> REDIS
        GEN --> FILES
        ANNO --> PG
    end
    
    subgraph "硬件资源"
        GPU[GPU加速<br/>NVIDIA RTX 3080+]
        CPU[CPU处理<br/>多核心]
        STORAGE[存储空间<br/>1TB+ SSD]
        
        SD --> GPU
        CUSTOM --> GPU
        FILES --> STORAGE
        PG --> STORAGE
    end
    
    style UI fill:#e1f5fe
    style API fill:#f3e5f5
    style SD fill:#fff3e0
    style PG fill:#e8f5e8
    style GPU fill:#ffebee


gantt
    title 军事目标图像数据集生成系统开发计划
    dateFormat  YYYY-MM-DD
    section 第一阶段：基础架构
    项目结构搭建     :done, arch1, 2024-01-01, 2024-01-03
    数据库设计       :done, arch2, 2024-01-01, 2024-01-05
    前后端框架       :done, arch3, 2024-01-03, 2024-01-07
    Docker部署       :done, arch4, 2024-01-05, 2024-01-08
    
    section 第二阶段：核心功能
    用户认证系统     :active, auth, 2024-01-08, 3d
    数据集管理API    :api, after auth, 4d
    图像生成引擎     :gen, after api, 5d
    自动标注系统     :anno, after gen, 4d
    前端界面开发     :ui, after auth, 7d
    
    section 第三阶段：AI集成
    SD模型集成       :ai1, after anno, 5d
    模型微调环境     :ai2, after ai1, 7d
    自定义模型训练   :ai3, after ai2, 10d
    性能优化         :ai4, after ai3, 5d
    
    section 第四阶段：系统完善
    集成测试         :test, after ai4, 5d
    部署优化         :deploy, after test, 3d
    文档完善         :doc, after deploy, 3d
    验收交付         :delivery, after doc, 2d