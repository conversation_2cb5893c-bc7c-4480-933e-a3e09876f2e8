# 军事目标图像数据集生成系统

## 项目概述

本系统是一个基于AI的军事目标图像数据集生成和管理平台，支持在不同天气和地形场景中生成自然、清晰、合理的模拟图像数据集。

## 功能特性

### 核心功能
- 🎯 **多场景图像生成**：支持坦克、战机、舰艇等军事目标
- 🌦️ **多天气模拟**：雨天、雪天、大雾、夜间等天气条件
- 🏞️ **多地形场景**：城市、岛屿、乡村等地形环境
- 🏷️ **智能标注**：支持分类标注和目标检测标注
- 📊 **数据集管理**：完整的增删改查功能
- 👥 **用户管理**：多角色权限控制和会话管理
- 🤖 **AI模型管理**：模型训练、评估和版本控制

### 生成方式
1. **传统代码图像合成**：基于规则的图像合成
2. **基准扩散模型**：使用Stable Diffusion生成
3. **定制训练模型**：专门训练的军事目标生成模型

### 数据集功能
- ✅ **自动分组**：训练集(80%)、验证集(10%)、测试集(10%)
- ✅ **标注预览**：支持边界框(bbox)可视化
- ✅ **批量生成**：用户可指定生成数量
- ✅ **组合配置**：灵活的目标、天气、场景组合

## 技术架构

### 后端技术栈
- **框架**：FastAPI + Python 3.9+
- **数据库**：PostgreSQL + Redis
- **ORM**：SQLAlchemy + Alembic
- **AI模型**：Stable Diffusion, Custom Models
- **图像处理**：OpenCV, PIL, NumPy

### 前端技术栈
- **框架**：React 18 + TypeScript
- **UI库**：Ant Design
- **状态管理**：Redux Toolkit
- **图像标注**：自研标注组件

### 部署方案
- **容器化**：Docker + Docker Compose
- **反向代理**：Nginx
- **GPU支持**：CUDA环境

## 数据库设计

### 核心表结构

#### 用户管理模块
- **users** - 用户基本信息
- **user_sessions** - 用户会话管理
- **user_activities** - 用户操作日志

#### 数据集管理模块
- **datasets** - 数据集基本信息
- **images** - 图像文件信息
- **annotations** - 图像标注数据
- **generation_tasks** - 图像生成任务

#### AI模型管理模块
- **ai_models** - AI模型信息
- **model_training_jobs** - 模型训练任务
- **model_evaluations** - 模型评估结果

#### 系统管理模块
- **system_configs** - 系统配置
- **system_stats** - 系统统计数据
- **system_logs** - 系统日志
- **task_queues** - 任务队列
- **file_storages** - 文件存储管理

### 数据库特性
- 🔗 **完整的关系设计**：外键约束确保数据一致性
- 📊 **索引优化**：关键字段建立索引提升查询性能
- 🕒 **时间戳追踪**：创建时间、更新时间自动管理
- 🔄 **软删除支持**：重要数据支持逻辑删除
- 📈 **统计分析**：内置数据统计和分析功能

## 项目状态

### 当前版本
- **版本**: v1.0.0-beta
- **状态**: 核心功能开发阶段
- **完成度**: 约75%

### ✅ 已完成功能
- ✅ **完整数据库设计** - 16个核心表，完整关系设计
- ✅ **用户认证系统** - JWT认证，角色权限管理
- ✅ **数据集管理** - 完整CRUD操作，统计分析
- ✅ **前端界面系统** - 仪表盘，数据集管理，生成控制台
- ✅ **API服务架构** - RESTful API设计和实现
- ✅ **Docker容器化** - 完整的开发和部署环境

### 🚧 开发中功能
- 🚧 **API路由集成** - 需要注册数据集和生成API
- 🚧 **图像上传系统** - 多文件上传和存储管理
- 🚧 **图像生成引擎** - 框架已搭建，需要AI模型集成
- 🚧 **传统图像合成** - 基于模板的图像合成

### 📋 计划功能
- 📋 **AI模型集成** - Stable Diffusion集成和自定义模型
- 📋 **自动标注系统** - COCO格式标注生成
- 📋 **批量处理系统** - 大规模图像生成和处理
- 📋 **监控系统** - 系统性能和任务监控

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+
- CUDA 11.8+ (可选，用于GPU加速)
- **Windows用户**：推荐安装Docker Desktop

### 一键启动

#### Linux/macOS 用户
```bash
git clone <repository-url>
cd pic_gen_tool
./start.sh
```

#### Windows 用户

**方式一：一键启动（最推荐）**
```cmd
git clone <repository-url>
cd pic_gen_tool
start-windows.bat
```

**特性**：
- 🎨 美观的用户界面设计
- 📊 6步进度显示
- 🔧 智能Python环境检查和修复
- 🚀 自动打开浏览器
- ✅ 完整的错误处理
- 🔄 PyTorch版本兼容性自动修复

**方式二：手动启动**
```cmd
git clone <repository-url>
cd pic_gen_tool
# 参考手动安装步骤
```

### 手动安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd pic_gen_tool
```

2. **后端环境设置**
```bash
cd backend
pip install -r requirements.txt
```

3. **前端环境设置**
```bash
cd frontend
npm install
```

4. **数据库初始化**
```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis

# 运行数据库迁移
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
alembic upgrade head
```

5. **启动服务**
```bash
# 启动后端服务
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 3002

# 启动前端服务
cd frontend
PORT=3001 npm start
```

### 停止服务

#### Linux/macOS
```bash
# 按 Ctrl+C 停止服务，或使用
docker-compose down
```

#### Windows
```cmd
# 使用停止脚本
stop-windows.bat
```

### 访问地址
- **前端应用**：http://localhost:3001
- **后端API**：http://localhost:3002
- **API文档**：http://localhost:3002/docs

## 项目结构

```
pic_gen_tool/
├── backend/                 # 后端服务
│   ├── app/                # 应用核心代码
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── ai_models/         # AI模型相关
│   ├── migrations/        # 数据库迁移
│   ├── alembic.ini        # 数据库迁移配置
│   └── requirements.txt   # Python依赖
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   └── utils/         # 工具函数
│   └── package.json       # Node.js依赖
├── docker-compose.yml      # Docker编排
├── start.sh               # Linux/macOS启动脚本
├── start-windows.bat      # Windows批处理启动脚本
├── stop-windows.bat       # Windows批处理停止脚本
├── fix-python-env.bat     # Python环境修复工具
├── README-Windows.md      # Windows环境详细指南
└── README.md              # 项目文档
```