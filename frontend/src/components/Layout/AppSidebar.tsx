import React from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  DatabaseOutlined,
  PictureOutlined,
  RobotOutlined,
  SettingOutlined,
  BarChartOutlined,
  UserOutlined,
  FileImageOutlined,
  ThunderboltOutlined,
  MonitorOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Sider } = Layout;

interface AppSidebarProps {
  collapsed: boolean;
}

type MenuItem = Required<MenuProps>['items'][number];

const AppSidebar: React.FC<AppSidebarProps> = ({ collapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 创建菜单项
  const createMenuItem = (
    key: string,
    icon: React.ReactNode,
    label: string,
    children?: MenuItem[]
  ): MenuItem => {
    return {
      key,
      icon,
      label,
      children,
    } as MenuItem;
  };

  // 菜单项配置
  const menuItems: MenuItem[] = [
    createMenuItem('/dashboard', <DashboardOutlined />, '仪表盘'),
    createMenuItem('/datasets', <DatabaseOutlined />, '数据集管理', [
      createMenuItem('/datasets/list', <FileImageOutlined />, '数据集列表'),
      createMenuItem('/datasets/create', <PictureOutlined />, '创建数据集'),
      createMenuItem('/datasets/import', <DatabaseOutlined />, '导入数据集'),
    ]),
    createMenuItem('/generation', <ThunderboltOutlined />, '图像生成', [
      createMenuItem('/generation/single', <PictureOutlined />, '单张生成'),
      createMenuItem('/generation/batch', <DatabaseOutlined />, '批量生成'),
      createMenuItem('/generation/tasks', <MonitorOutlined />, '生成任务'),
      createMenuItem('/generation/history', <BarChartOutlined />, '生成历史'),
    ]),
    createMenuItem('/models', <RobotOutlined />, 'AI模型管理', [
      createMenuItem('/models/list', <RobotOutlined />, '模型列表'),
      createMenuItem('/models/training', <ThunderboltOutlined />, '模型训练'),
      createMenuItem('/models/evaluation', <BarChartOutlined />, '模型评估'),
    ]),
    createMenuItem('/system', <SettingOutlined />, '系统管理', [
      createMenuItem('/system/users', <UserOutlined />, '用户管理'),
      createMenuItem('/system/configs', <SettingOutlined />, '系统配置'),
      createMenuItem('/system/logs', <FileImageOutlined />, '系统日志'),
      createMenuItem('/system/monitoring', <MonitorOutlined />, '系统监控'),
      createMenuItem('/system/storage', <DatabaseOutlined />, '存储管理'),
    ]),
  ];

  // 处理菜单点击
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    navigate(key);
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    
    // 精确匹配
    if (menuItems.some(item => item?.key === path)) {
      return [path];
    }
    
    // 查找子菜单匹配
    for (const item of menuItems) {
      if (item && 'children' in item && item.children) {
        for (const child of item.children) {
          if (child && child.key === path) {
            return [path];
          }
        }
      }
    }
    
    // 模糊匹配（用于嵌套路由）
    for (const item of menuItems) {
      if (item && typeof item.key === 'string' && path.startsWith(item.key)) {
        if ('children' in item && item.children) {
          for (const child of item.children) {
            if (child && typeof child.key === 'string' && path.startsWith(child.key)) {
              return [child.key];
            }
          }
        }
        return [item.key];
      }
    }
    
    return ['/dashboard'];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname;
    const openKeys: string[] = [];
    
    for (const item of menuItems) {
      if (item && 'children' in item && item.children) {
        for (const child of item.children) {
          if (child && typeof child.key === 'string' && path.startsWith(child.key)) {
            openKeys.push(item.key as string);
            break;
          }
        }
      }
    }
    
    return openKeys;
  };

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
      }}
      theme="dark"
    >
      {/* Logo区域 */}
      <div
        style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid #303030',
        }}
      >
        {collapsed ? (
          <RobotOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
        ) : (
          <div style={{ color: '#fff', fontSize: '16px', fontWeight: 'bold' }}>
            PicGen
          </div>
        )}
      </div>

      {/* 菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={getOpenKeys()}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ borderRight: 0 }}
      />
    </Sider>
  );
};

export default AppSidebar; 