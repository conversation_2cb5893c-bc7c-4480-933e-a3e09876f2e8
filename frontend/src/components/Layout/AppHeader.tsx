import React from 'react';
import { Layout, Button, Dropdown, Avatar, Space, Typography, Badge } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { User } from '../../types';

const { Header } = Layout;
const { Text } = Typography;

interface AppHeaderProps {
  user: User | null;
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  onLogout: () => void;
}

const AppHeader: React.FC<AppHeaderProps> = ({
  user,
  collapsed,
  onCollapse,
  onLogout,
}) => {
  // 用户菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: onLogout,
    },
  ];

  const handleUserMenuClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
      case 'profile':
        window.location.href = '/profile';
        break;
      case 'settings':
        // 处理设置
        break;
      case 'logout':
        onLogout();
        break;
    }
  };

  return (
    <Header
      style={{
        padding: '0 16px',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      {/* 左侧：折叠按钮和标题 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => onCollapse(!collapsed)}
          style={{
            fontSize: '16px',
            width: 64,
            height: 64,
          }}
        />
        <Typography.Title level={4} style={{ margin: 0, marginLeft: 16 }}>
          军事目标图像数据集生成系统
        </Typography.Title>
      </div>

      {/* 右侧：通知和用户信息 */}
      <Space size="middle">
        {/* 通知铃铛 */}
        <Badge count={0} size="small">
          <Button
            type="text"
            icon={<BellOutlined />}
            style={{ fontSize: '16px' }}
          />
        </Badge>

        {/* 用户信息 */}
        {user && (
          <Dropdown
            menu={{
              items: userMenuItems,
              onClick: handleUserMenuClick,
            }}
            placement="bottomRight"
            arrow
          >
            <Space style={{ cursor: 'pointer' }}>
              <Avatar
                size="small"
                icon={<UserOutlined />}
                src={user.avatar_url}
              />
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <Text strong>{user.full_name || user.username}</Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {user.role === 'admin' ? '管理员' : 
                   user.role === 'researcher' ? '研究员' : '查看者'}
                </Text>
              </div>
            </Space>
          </Dropdown>
        )}
      </Space>
    </Header>
  );
};

export default AppHeader; 