import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Row,
  Col,
  Typography,
  message,
  Modal,
  Progress,
  Tooltip,
  Dropdown,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  MoreOutlined,
  DatabaseOutlined,
  PictureOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import { useNavigate } from 'react-router-dom';
import datasetService from '../../services/datasetService';
import { Dataset, PaginatedResponse } from '../../types';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const DatasetList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    status: '',
  });

  // 使用useCallback包装loadDatasets函数
  const loadDatasets = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        search: filters.search || undefined,
        status: filters.status || undefined,
      };

      const response: PaginatedResponse<Dataset> = await datasetService.getDatasets(params);
      
      setDatasets(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total,
      }));
    } catch (error) {
      console.error('Failed to load datasets:', error);
      message.error('加载数据集列表失败');
    } finally {
      setLoading(false);
    }
  }, [pagination.current, pagination.pageSize, filters.search, filters.status]);

  // 加载数据集列表
  useEffect(() => {
    loadDatasets();
  }, [loadDatasets]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理状态筛选
  const handleStatusFilter = (value: string) => {
    setFilters(prev => ({ ...prev, status: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize,
    }));
  };

  // 删除数据集
  const handleDelete = async (dataset: Dataset) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除数据集 "${dataset.name}" 吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await datasetService.deleteDataset(dataset.id);
          message.success('数据集删除成功');
          loadDatasets();
        } catch (error) {
          console.error('Failed to delete dataset:', error);
          message.error('删除数据集失败');
        }
      },
    });
  };

  // 导出数据集
  const handleExport = async (dataset: Dataset) => {
    try {
      const blob = await datasetService.exportDataset(dataset.id, 'coco');
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${dataset.name}_export.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('数据集导出成功');
    } catch (error) {
      console.error('Failed to export dataset:', error);
      message.error('导出数据集失败');
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      creating: 'processing',
      active: 'success',
      completed: 'default',
      archived: 'warning',
      error: 'error',
    };
    return colorMap[status] || 'default';
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      creating: '创建中',
      active: '活跃',
      completed: '已完成',
      archived: '已归档',
      error: '错误',
    };
    return textMap[status] || status;
  };

  // 操作菜单
  const getActionMenu = (record: Dataset): MenuProps['items'] => [
    {
      key: 'view',
      icon: <EyeOutlined />,
      label: '查看详情',
      onClick: () => navigate(`/datasets/${record.id}`),
    },
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '编辑',
      onClick: () => navigate(`/datasets/${record.id}/edit`),
    },
    {
      key: 'export',
      icon: <DownloadOutlined />,
      label: '导出',
      onClick: () => handleExport(record),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      danger: true,
      onClick: () => handleDelete(record),
    },
  ];

  // 表格列配置
  const columns: ColumnsType<Dataset> = [
    {
      title: '数据集名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Dataset) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1890ff' }} />
          <Button
            type="link"
            onClick={() => navigate(`/datasets/${record.id}`)}
            style={{ padding: 0, height: 'auto' }}
          >
            <Text strong>{text}</Text>
          </Button>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text type="secondary">{text || '暂无描述'}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '图像数量',
      key: 'images',
      width: 120,
      render: (_: any, record: Dataset) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.generated_images}/{record.total_images}</Text>
          <Progress
            percent={record.total_images > 0 ? (record.generated_images / record.total_images) * 100 : 0}
            size="small"
            showInfo={false}
          />
        </Space>
      ),
    },
    {
      title: '生成方法',
      dataIndex: 'generation_method',
      key: 'generation_method',
      width: 120,
      render: (method: string) => {
        const methodMap: Record<string, { text: string; color: string }> = {
          traditional: { text: '传统方法', color: 'default' },
          stable_diffusion: { text: 'Stable Diffusion', color: 'blue' },
          custom_model: { text: '自定义模型', color: 'purple' },
        };
        const config = methodMap[method] || { text: method, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '目标类型',
      dataIndex: 'targets',
      key: 'targets',
      width: 150,
      render: (targets: string[]) => (
        <Space wrap>
          {targets.slice(0, 2).map(target => (
            <Tag key={target}>{target}</Tag>
          ))}
          {targets.length > 2 && (
            <Tag>+{targets.length - 2}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => (
        <Text type="secondary">
          {new Date(date).toLocaleDateString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: Dataset) => (
        <Dropdown
          menu={{ items: getActionMenu(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            数据集管理
          </Title>
          <Text type="secondary">管理和查看所有图像数据集</Text>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/datasets/create')}
          >
            创建数据集
          </Button>
        </Col>
      </Row>

      {/* 筛选和搜索 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索数据集名称..."
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="筛选状态"
              allowClear
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
            >
              <Option value="creating">创建中</Option>
              <Option value="active">活跃</Option>
              <Option value="completed">已完成</Option>
              <Option value="archived">已归档</Option>
              <Option value="error">错误</Option>
            </Select>
          </Col>
          <Col xs={24} sm={24} md={10}>
            <Space>
              <Text type="secondary">
                共 {pagination.total} 个数据集
              </Text>
              <Button onClick={loadDatasets}>刷新</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={datasets}
          loading={loading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default DatasetList; 