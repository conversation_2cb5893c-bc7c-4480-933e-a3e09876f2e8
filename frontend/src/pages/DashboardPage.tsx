import React from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Progress,
  Table,
  Tag,
  Space,
  Button,
} from 'antd';
import {
  DatabaseOutlined,
  PictureOutlined,
  RobotOutlined,
  UserOutlined,
  ArrowUpOutlined,
  EyeOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  // 模拟数据
  const mockDatasets = [
    {
      id: 1,
      name: '坦克识别数据集',
      status: 'active',
      total_images: 1200,
    },
    {
      id: 2,
      name: '装甲车辆数据集',
      status: 'completed',
      total_images: 800,
    },
    {
      id: 3,
      name: '军用飞机数据集',
      status: 'creating',
      total_images: 450,
    },
  ];

  const mockTasks = [
    {
      id: 1,
      status: 'running',
      progress: 0.75,
    },
    {
      id: 2,
      status: 'completed',
      progress: 1.0,
    },
    {
      id: 3,
      status: 'pending',
      progress: 0.0,
    },
  ];

  // 数据集状态颜色映射
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      creating: 'processing',
      active: 'success',
      completed: 'default',
      archived: 'warning',
      error: 'error',
      running: 'processing',
      pending: 'default',
    };
    return colorMap[status] || 'default';
  };

  // 数据集状态文本映射
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      creating: '创建中',
      active: '活跃',
      completed: '已完成',
      archived: '已归档',
      error: '错误',
      running: '运行中',
      pending: '等待中',
    };
    return textMap[status] || status;
  };

  // 数据集表格列配置
  const datasetColumns = [
    {
      title: '数据集名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '图像数量',
      dataIndex: 'total_images',
      key: 'total_images',
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" size="small" icon={<EyeOutlined />}>
            查看
          </Button>
        </Space>
      ),
    },
  ];

  // 生成任务表格列配置
  const taskColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      render: (id: number) => <Text code>#{id}</Text>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={Math.round(progress * 100)} size="small" />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" size="small" icon={<PlayCircleOutlined />}>
            查看
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        系统仪表盘
      </Title>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="数据集总数"
              value={12}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={
                <span style={{ fontSize: '14px' }}>
                  <ArrowUpOutlined /> 3
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="图像总数"
              value={2450}
              prefix={<PictureOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <span style={{ fontSize: '14px' }}>
                  <ArrowUpOutlined /> 150
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="AI模型数"
              value={8}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={
                <span style={{ fontSize: '14px' }}>
                  <ArrowUpOutlined /> 2
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={24}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#fa541c' }}
              suffix={
                <span style={{ fontSize: '14px' }}>
                  <ArrowUpOutlined /> 5
                </span>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card 
            title="最近数据集" 
            extra={<Button type="link">查看全部</Button>}
          >
            <Table
              dataSource={mockDatasets}
              columns={datasetColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card 
            title="最近生成任务" 
            extra={<Button type="link">查看全部</Button>}
          >
            <Table
              dataSource={mockTasks}
              columns={taskColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>

      {/* 系统状态 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="系统状态">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">存储使用率</Text>
                  <Progress
                    type="circle"
                    percent={75}
                    format={() => '75%'}
                    style={{ marginTop: 8 }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">内存使用率</Text>
                  <Progress
                    type="circle"
                    percent={60}
                    format={() => '60%'}
                    style={{ marginTop: 8 }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">CPU使用率</Text>
                  <Progress
                    type="circle"
                    percent={45}
                    format={() => '45%'}
                    style={{ marginTop: 8 }}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
