import React from 'react';
import { Card, Typography, Descriptions, Avatar, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title } = Typography;

const ProfilePage: React.FC = () => {
  const { user } = useAuth();

  return (
    <div>
      <Title level={2}>个人资料</Title>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <Avatar size={80} icon={<UserOutlined />} />
            <Title level={4} style={{ marginTop: 16 }}>
              {user?.full_name || user?.username}
            </Title>
          </div>
          
          <Descriptions title="基本信息" bordered column={1}>
            <Descriptions.Item label="用户名">
              {user?.username}
            </Descriptions.Item>
            <Descriptions.Item label="邮箱">
              {user?.email}
            </Descriptions.Item>
            <Descriptions.Item label="姓名">
              {user?.full_name || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="角色">
              {user?.role === 'admin' ? '管理员' : 
               user?.role === 'researcher' ? '研究员' : '用户'}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {user?.is_active ? '活跃' : '禁用'}
            </Descriptions.Item>
            <Descriptions.Item label="注册时间">
              {user?.created_at ? new Date(user.created_at).toLocaleString() : '未知'}
            </Descriptions.Item>
          </Descriptions>
        </Space>
      </Card>
    </div>
  );
};

export default ProfilePage;