/**
 * 系统管理服务
 */

import apiClient from './api';
import { SystemStats, User, PaginatedResponse } from '../types';

class SystemService {
  // 获取系统统计信息
  async getSystemStats(params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<SystemStats> {
    const response = await apiClient.get('/system/stats', { params });
    return response.data;
  }

  // 获取系统健康状态
  async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'error';
    services: {
      database: boolean;
      redis: boolean;
      storage: boolean;
      ai_models: boolean;
    };
    uptime: number;
    memory_usage: number;
    disk_usage: number;
    cpu_usage: number;
  }> {
    const response = await apiClient.get('/system/health');
    return response.data;
  }

  // 获取系统配置
  async getSystemConfigs(): Promise<any> {
    const response = await apiClient.get('/system/configs');
    return response.data;
  }

  // 更新系统配置
  async updateSystemConfig(key: string, value: any): Promise<void> {
    await apiClient.put('/system/configs', { key, value });
  }

  // 获取系统日志
  async getSystemLogs(params?: {
    page?: number;
    size?: number;
    level?: string;
    module?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<PaginatedResponse<any>> {
    const response = await apiClient.get('/system/logs', { params });
    return response.data;
  }

  // 获取用户管理列表
  async getUsers(params?: {
    page?: number;
    size?: number;
    role?: string;
    status?: string;
    search?: string;
  }): Promise<PaginatedResponse<User>> {
    const response = await apiClient.get('/system/users', { params });
    return response.data;
  }

  // 创建用户
  async createUser(data: {
    username: string;
    email: string;
    password: string;
    full_name?: string;
    role: string;
  }): Promise<User> {
    const response = await apiClient.post('/system/users', data);
    return response.data;
  }

  // 更新用户
  async updateUser(id: number, data: Partial<User>): Promise<User> {
    const response = await apiClient.put(`/system/users/${id}`, data);
    return response.data;
  }

  // 删除用户
  async deleteUser(id: number): Promise<void> {
    await apiClient.delete(`/system/users/${id}`);
  }

  // 重置用户密码
  async resetUserPassword(id: number, newPassword: string): Promise<void> {
    await apiClient.post(`/system/users/${id}/reset-password`, {
      new_password: newPassword,
    });
  }

  // 获取用户活动日志
  async getUserActivities(userId?: number, params?: {
    page?: number;
    size?: number;
    action?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<PaginatedResponse<any>> {
    const response = await apiClient.get('/system/user-activities', {
      params: { user_id: userId, ...params },
    });
    return response.data;
  }

  // 获取任务队列状态
  async getTaskQueues(params?: {
    page?: number;
    size?: number;
    status?: string;
    task_type?: string;
  }): Promise<PaginatedResponse<any>> {
    const response = await apiClient.get('/system/task-queues', { params });
    return response.data;
  }

  // 重试失败任务
  async retryTask(taskId: string): Promise<void> {
    await apiClient.post(`/system/task-queues/${taskId}/retry`);
  }

  // 取消任务
  async cancelTask(taskId: string): Promise<void> {
    await apiClient.post(`/system/task-queues/${taskId}/cancel`);
  }

  // 清理任务队列
  async cleanupTasks(status: string): Promise<{ cleaned_count: number }> {
    const response = await apiClient.post('/system/task-queues/cleanup', { status });
    return response.data;
  }

  // 获取存储使用情况
  async getStorageUsage(): Promise<{
    total_storage: number;
    used_storage: number;
    available_storage: number;
    image_storage: number;
    model_storage: number;
    temp_storage: number;
    storage_breakdown: any[];
  }> {
    const response = await apiClient.get('/system/storage');
    return response.data;
  }

  // 清理临时文件
  async cleanupTempFiles(): Promise<{ cleaned_size: number; cleaned_files: number }> {
    const response = await apiClient.post('/system/storage/cleanup');
    return response.data;
  }

  // 备份数据库
  async backupDatabase(): Promise<{ backup_file: string; backup_size: number }> {
    const response = await apiClient.post('/system/backup');
    return response.data;
  }

  // 获取备份列表
  async getBackups(): Promise<any[]> {
    const response = await apiClient.get('/system/backups');
    return response.data;
  }

  // 恢复数据库
  async restoreDatabase(backupFile: string): Promise<void> {
    await apiClient.post('/system/restore', { backup_file: backupFile });
  }

  // 获取系统监控数据
  async getMonitoringData(params?: {
    metric: string;
    start_time?: string;
    end_time?: string;
    interval?: string;
  }): Promise<any> {
    const response = await apiClient.get('/system/monitoring', { params });
    return response.data;
  }

  // 发送系统通知
  async sendNotification(data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    target_users?: number[];
  }): Promise<void> {
    await apiClient.post('/system/notifications', data);
  }

  // 获取系统版本信息
  async getVersionInfo(): Promise<{
    version: string;
    build_date: string;
    git_commit: string;
    python_version: string;
    dependencies: any;
  }> {
    const response = await apiClient.get('/system/version');
    return response.data;
  }
}

const systemService = new SystemService();
export default systemService; 