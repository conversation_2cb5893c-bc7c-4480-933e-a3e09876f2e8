/**
 * Mock认证服务 - 用于开发环境测试
 */

import { LoginRequest, LoginResponse, User } from '../types';

// 预置测试用户
const MOCK_USERS: User[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: '系统管理员',
    role: 'admin',
    avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    phone: '13800138001',
    organization: '军事科技研究院',
    position: '系统管理员',
    bio: '负责系统管理和维护工作',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    datasets_count: 15,
    generated_images_count: 1250,
  },
  {
    id: 2,
    username: 'user',
    email: '<EMAIL>',
    full_name: '普通用户',
    role: 'user',
    avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user',
    phone: '13800138002',
    organization: '图像处理部门',
    position: '数据分析师',
    bio: '专注于军事目标图像数据分析',
    is_active: true,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    datasets_count: 8,
    generated_images_count: 680,
  },
  {
    id: 3,
    username: 'researcher',
    email: '<EMAIL>',
    full_name: '研究员',
    role: 'researcher',
    avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=researcher',
    phone: '13800138003',
    organization: 'AI研究中心',
    position: '高级研究员',
    bio: '专注于深度学习和计算机视觉研究',
    is_active: true,
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z',
    datasets_count: 25,
    generated_images_count: 3200,
  },
];

// 预置密码映射
const MOCK_PASSWORDS: Record<string, string> = {
  admin: 'admin123',
  user: 'user123',
  researcher: 'research123',
};

class MockAuthService {
  private currentUser: User | null = null;

  // 模拟延迟
  private delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 生成Mock JWT Token
  private generateMockToken(user: User): string {
    const payload = {
      user_id: user.id,
      username: user.username,
      role: user.role,
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24小时过期
    };
    return `mock.${btoa(JSON.stringify(payload))}.signature`;
  }

  // 验证Mock Token
  private validateMockToken(token: string): User | null {
    try {
      if (!token.startsWith('mock.')) return null;
      
      const payloadStr = token.split('.')[1];
      const payload = JSON.parse(atob(payloadStr));
      
      // 检查是否过期
      if (payload.exp < Math.floor(Date.now() / 1000)) {
        return null;
      }
      
      // 查找用户
      return MOCK_USERS.find(u => u.id === payload.user_id) || null;
    } catch {
      return null;
    }
  }

  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    await this.delay();

    const { username, password } = credentials;
    
    // 查找用户
    const user = MOCK_USERS.find(u => u.username === username);
    if (!user) {
      throw new Error('用户名不存在');
    }

    // 验证密码
    if (MOCK_PASSWORDS[username] !== password) {
      throw new Error('密码错误');
    }

    // 检查用户状态
    if (!user.is_active) {
      throw new Error('账户已被禁用');
    }

    // 生成token
    const access_token = this.generateMockToken(user);
    
    // 保存当前用户
    this.currentUser = user;
    
    // 保存到localStorage
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('user', JSON.stringify(user));

    return {
      access_token,
      token_type: 'Bearer',
      expires_in: 86400,
      user,
    };
  }

  // 用户登出
  async logout(): Promise<void> {
    await this.delay(200);
    
    this.currentUser = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    await this.delay(300);

    const token = localStorage.getItem('access_token');
    if (!token) {
      throw new Error('未登录');
    }

    const user = this.validateMockToken(token);
    if (!user) {
      throw new Error('Token无效或已过期');
    }

    this.currentUser = user;
    return user;
  }

  // 更新用户信息
  async updateProfile(userData: Partial<User>): Promise<User> {
    await this.delay();

    if (!this.currentUser) {
      throw new Error('未登录');
    }

    // 更新用户信息
    const updatedUser = { ...this.currentUser, ...userData };
    
    // 更新MOCK_USERS数组中的用户
    const userIndex = MOCK_USERS.findIndex(u => u.id === this.currentUser!.id);
    if (userIndex !== -1) {
      MOCK_USERS[userIndex] = updatedUser;
    }

    this.currentUser = updatedUser;
    localStorage.setItem('user', JSON.stringify(updatedUser));

    return updatedUser;
  }

  // 上传头像
  async uploadAvatar(formData: FormData): Promise<{ avatar_url: string }> {
    await this.delay(1000);

    if (!this.currentUser) {
      throw new Error('未登录');
    }

    // 模拟头像上传，生成新的头像URL
    const timestamp = Date.now();
    const avatar_url = `https://api.dicebear.com/7.x/avataaars/svg?seed=${this.currentUser.username}-${timestamp}`;

    return { avatar_url };
  }

  // 修改密码
  async changePassword(data: {
    current_password: string;
    new_password: string;
  }): Promise<void> {
    await this.delay();

    if (!this.currentUser) {
      throw new Error('未登录');
    }

    // 验证当前密码
    if (MOCK_PASSWORDS[this.currentUser.username] !== data.current_password) {
      throw new Error('当前密码错误');
    }

    // 更新密码
    MOCK_PASSWORDS[this.currentUser.username] = data.new_password;
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token');
    if (!token) return false;
    
    const user = this.validateMockToken(token);
    return !!user;
  }

  // 获取本地存储的用户信息
  getLocalUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  // 获取token
  getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  // 获取所有测试账户信息（仅用于展示）
  getTestAccounts(): Array<{ username: string; password: string; role: string; description: string }> {
    return [
      {
        username: 'admin',
        password: 'admin123',
        role: '系统管理员',
        description: '拥有所有权限，可以管理用户和系统设置',
      },
      {
        username: 'user',
        password: 'user123',
        role: '普通用户',
        description: '可以创建数据集和生成图像',
      },
      {
        username: 'researcher',
        password: 'research123',
        role: '研究员',
        description: '专注于模型训练和研究工作',
      },
    ];
  }
}

const mockAuthService = new MockAuthService();
export default mockAuthService;
