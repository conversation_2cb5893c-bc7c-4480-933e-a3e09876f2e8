/**
 * 图像生成服务
 */

import apiClient from './api';
import { GenerationTask, CreateGenerationTaskRequest, PaginatedResponse } from '../types';

class GenerationService {
  // 获取生成任务列表
  async getGenerationTasks(params?: {
    page?: number;
    size?: number;
    status?: string;
    dataset_id?: number;
  }): Promise<PaginatedResponse<GenerationTask>> {
    const response = await apiClient.get('/generation/tasks', { params });
    return response.data;
  }

  // 获取生成任务详情
  async getGenerationTask(id: number): Promise<GenerationTask> {
    const response = await apiClient.get(`/generation/tasks/${id}`);
    return response.data;
  }

  // 获取生成配置选项
  async getGenerationOptions(): Promise<{
    targets: string[];
    weather_conditions: string[];
    terrain_scenes: string[];
    generation_methods: string[];
  }> {
    const response = await apiClient.get('/generation/options');
    return response.data;
  }

  // 创建生成任务
  async createGenerationTask(data: CreateGenerationTaskRequest): Promise<GenerationTask> {
    const response = await apiClient.post('/generation/tasks', data);
    return response.data;
  }

  // 取消生成任务
  async cancelGenerationTask(id: number): Promise<void> {
    await apiClient.post(`/generation/tasks/${id}/cancel`);
  }

  // 重试生成任务
  async retryGenerationTask(id: number): Promise<GenerationTask> {
    const response = await apiClient.post(`/generation/tasks/${id}/retry`);
    return response.data;
  }

  // 获取任务进度
  async getTaskProgress(id: number): Promise<{
    progress: number;
    status: string;
    generated_images: number;
    failed_images: number;
    current_step?: string;
  }> {
    const response = await apiClient.get(`/generation/tasks/${id}/progress`);
    return response.data;
  }

  // 批量生成图像
  async batchGenerate(data: {
    dataset_id: number;
    targets: string[];
    weather_conditions: string[];
    terrain_scenes: string[];
    images_per_combination: number;
    generation_method: string;
    model_id?: number;
    generation_params?: any;
  }): Promise<GenerationTask> {
    const response = await apiClient.post('/generation/batch', data);
    return response.data;
  }

  // 单张图像生成
  async generateSingleImage(data: {
    target: string;
    weather_condition: string;
    terrain_scene: string;
    generation_method: string;
    model_id?: number;
    generation_params?: any;
  }): Promise<{ image_url: string; generation_time: number }> {
    const response = await apiClient.post('/generation/single', data);
    return response.data;
  }

  // 预览生成效果
  async previewGeneration(data: {
    target: string;
    weather_condition: string;
    terrain_scene: string;
    generation_method: string;
    model_id?: number;
  }): Promise<{ preview_url: string }> {
    const response = await apiClient.post('/generation/preview', data);
    return response.data;
  }

  // 获取生成历史
  async getGenerationHistory(params?: {
    page?: number;
    size?: number;
    user_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<PaginatedResponse<GenerationTask>> {
    const response = await apiClient.get('/generation/history', { params });
    return response.data;
  }

  // 获取生成统计
  async getGenerationStats(params?: {
    start_date?: string;
    end_date?: string;
    group_by?: 'day' | 'week' | 'month';
  }): Promise<any> {
    const response = await apiClient.get('/generation/stats', { params });
    return response.data;
  }
}

const generationService = new GenerationService();
export default generationService; 