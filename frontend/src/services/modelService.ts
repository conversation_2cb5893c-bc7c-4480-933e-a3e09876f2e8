/**
 * AI模型管理服务
 */

import apiClient from './api';
import { AIModel, PaginatedResponse } from '../types';

class ModelService {
  // 获取模型列表
  async getModels(params?: {
    page?: number;
    size?: number;
    model_type?: string;
    status?: string;
    search?: string;
  }): Promise<PaginatedResponse<AIModel>> {
    const response = await apiClient.get('/models', { params });
    return response.data;
  }

  // 获取模型详情
  async getModel(id: number): Promise<AIModel> {
    const response = await apiClient.get(`/models/${id}`);
    return response.data;
  }

  // 创建模型
  async createModel(data: {
    name: string;
    description?: string;
    model_type: string;
    config?: any;
    supported_targets?: string[];
    supported_weather?: string[];
    supported_terrain?: string[];
  }): Promise<AIModel> {
    const response = await apiClient.post('/models', data);
    return response.data;
  }

  // 更新模型
  async updateModel(id: number, data: Partial<AIModel>): Promise<AIModel> {
    const response = await apiClient.put(`/models/${id}`, data);
    return response.data;
  }

  // 删除模型
  async deleteModel(id: number): Promise<void> {
    await apiClient.delete(`/models/${id}`);
  }

  // 上传模型文件
  async uploadModelFile(id: number, file: File): Promise<AIModel> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`/models/${id}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 下载模型文件
  async downloadModel(id: number): Promise<Blob> {
    const response = await apiClient.get(`/models/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // 测试模型
  async testModel(id: number, testData: {
    target: string;
    weather_condition: string;
    terrain_scene: string;
    generation_params?: any;
  }): Promise<{
    success: boolean;
    result_url?: string;
    inference_time: number;
    error_message?: string;
  }> {
    const response = await apiClient.post(`/models/${id}/test`, testData);
    return response.data;
  }

  // 获取模型训练任务
  async getTrainingJobs(modelId?: number, params?: {
    page?: number;
    size?: number;
    status?: string;
  }): Promise<PaginatedResponse<any>> {
    const response = await apiClient.get('/models/training-jobs', {
      params: { model_id: modelId, ...params },
    });
    return response.data;
  }

  // 创建训练任务
  async createTrainingJob(data: {
    model_id: number;
    job_name: string;
    training_config: any;
    dataset_config: any;
  }): Promise<any> {
    const response = await apiClient.post('/models/training-jobs', data);
    return response.data;
  }

  // 获取训练任务详情
  async getTrainingJob(id: number): Promise<any> {
    const response = await apiClient.get(`/models/training-jobs/${id}`);
    return response.data;
  }

  // 停止训练任务
  async stopTrainingJob(id: number): Promise<void> {
    await apiClient.post(`/models/training-jobs/${id}/stop`);
  }

  // 获取训练日志
  async getTrainingLogs(id: number): Promise<{ logs: string }> {
    const response = await apiClient.get(`/models/training-jobs/${id}/logs`);
    return response.data;
  }

  // 获取模型评估结果
  async getModelEvaluations(modelId: number): Promise<any[]> {
    const response = await apiClient.get(`/models/${modelId}/evaluations`);
    return response.data;
  }

  // 创建模型评估
  async createModelEvaluation(data: {
    model_id: number;
    evaluation_name: string;
    test_dataset_id?: number;
  }): Promise<any> {
    const response = await apiClient.post('/models/evaluations', data);
    return response.data;
  }

  // 获取模型使用统计
  async getModelStats(id: number): Promise<{
    usage_count: number;
    success_rate: number;
    avg_inference_time: number;
    recent_usage: any[];
  }> {
    const response = await apiClient.get(`/models/${id}/stats`);
    return response.data;
  }

  // 获取可用模型类型
  async getModelTypes(): Promise<string[]> {
    const response = await apiClient.get('/models/types');
    return response.data;
  }

  // 克隆模型
  async cloneModel(id: number, newName: string): Promise<AIModel> {
    const response = await apiClient.post(`/models/${id}/clone`, { name: newName });
    return response.data;
  }

  // 发布模型
  async publishModel(id: number): Promise<AIModel> {
    const response = await apiClient.post(`/models/${id}/publish`);
    return response.data;
  }

  // 废弃模型
  async deprecateModel(id: number): Promise<AIModel> {
    const response = await apiClient.post(`/models/${id}/deprecate`);
    return response.data;
  }
}

const modelService = new ModelService();
export default modelService; 